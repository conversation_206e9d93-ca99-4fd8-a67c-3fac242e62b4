server:
  port: 11091
  servlet:
    context-path: /api
    session:
      cookie:
        max-age: 2592000 # Cookie 30 天过期
        path: /api
spring:
  application:
    name: micode-backend
  profiles:
    active: dev
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher # 支持 swagger 3
  session:
    store-type: redis
    timeout: 2592000 # Session 30 天过期
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ***********************************************
    username: root
    password: root
#  datasource:
#    driver-class-name: com.mysql.jdbc.Driver
#    url: ******************************************************
#    username: yanweiyi
#    password: QUmPCn8vISxCoOY2
  redis:
    database: 0
    host: redis-13581.c281.us-east-1-2.ec2.redns.redis-cloud.com
    port: 13581
    password: MdecI02N9ksWMoEJPif4qrQaFaDSGO2B
  rabbitmq:
    host: fuji-01.lmq.cloudamqp.com
    port: 5672
    username: fqddcfny
    password: CkwX_KaHKPM9YXycCaqG9EeVRzTADix-
    virtual-host: fqddcfny
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # MyBatis 输出日志
knife4j:
  enable: true
  openapi:
    title: "MiCoder 接口文档"
    version: 1.0
    group:
      default:
        api-rule: package
        api-rule-resources:
          - com.yanweiyi.micodebackend.controller
# 代码沙箱配置
codesandbox:
  type: remote
  remote:
    url: http://*************:11090/executeCode
