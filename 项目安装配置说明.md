# MiCode 在线编程平台 - 详细安装配置说明

## 项目概述

MiCode 是一个基于微服务架构的在线编程平台，支持代码编写、在线判题、题目管理等功能。项目采用前后端分离架构，包含三个主要模块：

- **micode-frontend**: 前端界面（Vue 3 + TypeScript + Element Plus）
- **micode-backend**: 后端服务（Spring Boot + MyBatis Plus）
- **micode-code-sandbox**: 代码沙箱服务（Spring Boot + Docker）

## 系统要求

### 开发环境版本要求

- **Java**: JDK 1.8 或以上版本
- **Node.js**: 14.x 或以上版本
- **npm**: 6.x 或以上版本
- **Maven**: 3.6.x 或以上版本
- **MySQL**: 5.7 或以上版本
- **Redis**: 5.0 或以上版本
- **RabbitMQ**: 3.8 或以上版本
- **Docker**: 20.x 或以上版本（用于代码沙箱）

### 推荐开发工具

- **IDE**: IntelliJ IDEA 2020.3+ 或 Eclipse
- **前端开发**: VS Code
- **数据库管理**: Navicat 或 MySQL Workbench
- **API 测试**: Postman 或 Apifox

## 详细安装步骤

### 1. 环境准备

#### 1.1 安装 Java 环境
```bash
# 下载并安装 JDK 1.8
# 配置环境变量 JAVA_HOME
# 验证安装
java -version
javac -version
```

#### 1.2 安装 Node.js 和 npm
```bash
# 下载并安装 Node.js 14.x+
# 验证安装
node -v
npm -v

# 配置 npm 镜像源（可选）
npm config set registry https://registry.npmmirror.com/
```

#### 1.3 安装 Maven
```bash
# 下载并安装 Maven 3.6.x+
# 配置环境变量 MAVEN_HOME
# 验证安装
mvn -version
```

#### 1.4 安装 Docker
```bash
# 安装 Docker Desktop（Windows/Mac）或 Docker Engine（Linux）
# 启动 Docker 服务
# 验证安装
docker --version
docker-compose --version
```

### 2. 数据库配置

#### 2.1 MySQL 数据库设置
```sql
-- 创建数据库
CREATE DATABASE codedb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'micode'@'localhost' IDENTIFIED BY 'micode123';
GRANT ALL PRIVILEGES ON codedb.* TO 'micode'@'localhost';
FLUSH PRIVILEGES;
```

#### 2.2 执行数据库初始化脚本
```bash
# 进入项目目录
cd micode-backend/sql

# 执行建表脚本
mysql -u root -p codedb < create.sql
```

**默认数据库配置**:
- 数据库名: `codedb`
- 用户名: `root`
- 密码: `root`
- 端口: `3306`

#### 2.3 Redis 配置
项目使用云端 Redis 服务，配置信息：
- 主机: `redis-13581.c281.us-east-1-2.ec2.redns.redis-cloud.com`
- 端口: `13581`
- 密码: `MdecI02N9ksWMoEJPif4qrQaFaDSGO2B`

**本地 Redis 配置（可选）**:
如需使用本地 Redis，请修改 `micode-backend/src/main/resources/application.yml`:
```yaml
spring:
  redis:
    database: 0
    host: localhost
    port: 6379
    password: # 本地Redis密码，如无密码则留空
```

#### 2.4 RabbitMQ 配置

**推荐使用本地 RabbitMQ**:

1. **安装 RabbitMQ**:
```bash
# Windows: 下载并安装 RabbitMQ Server
# Mac: brew install rabbitmq
# Ubuntu: sudo apt-get install rabbitmq-server
# CentOS: sudo yum install rabbitmq-server
```

2. **启动 RabbitMQ 服务**:
```bash
# Windows: 通过服务管理器启动
# Linux/Mac:
sudo systemctl start rabbitmq-server
# 或
sudo rabbitmq-server start
```

3. **启用管理界面**:
```bash
sudo rabbitmq-plugins enable rabbitmq_management
```
访问管理界面: http://localhost:15672 (默认用户名/密码: guest/guest)

**本地 RabbitMQ 配置**:
修改 `micode-backend/src/main/resources/application.yml`:
```yaml
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
```

**当前云端配置** (如需使用):
- 主机: `fuji-01.lmq.cloudamqp.com`
- 端口: `5672`
- 用户名: `fqddcfny`
- 密码: `CkwX_KaHKPM9YXycCaqG9EeVRzTADix-`
- 虚拟主机: `fqddcfny`

### 3. 项目配置

#### 3.1 后端服务配置 (micode-backend)

**端口配置**: 11091
**上下文路径**: `/api`

**关键配置文件**: `micode-backend/src/main/resources/application.yml`

**数据库连接配置**:
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ***********************************************
    username: root
    password: root
```

**代码沙箱配置**:
```yaml
codesandbox:
  type: remote
  remote:
    url: http://*************:11090/executeCode
```

#### 3.2 代码沙箱服务配置 (micode-code-sandbox)

**端口配置**: 11090

**Docker 镜像**: `openjdk:8-alpine`

**关键配置文件**: `micode-code-sandbox/src/main/resources/application.yml`

#### 3.3 前端服务配置 (micode-frontend)

**开发端口**: 默认 8080
**生产构建**: 静态文件部署

**关键配置文件**:
- `package.json`: 依赖管理
- `vue.config.js`: Vue CLI 配置
- `tsconfig.json`: TypeScript 配置

### 4. 启动步骤

#### 4.1 启动后端服务
```bash
# 进入后端项目目录
cd micode-backend

# 安装依赖并编译
mvn clean install

# 启动服务
mvn spring-boot:run
# 或者
java -jar target/micode-backend-0.0.1-SNAPSHOT.jar
```

**验证**: 访问 http://localhost:11091/api/doc.html 查看 API 文档

#### 4.2 启动代码沙箱服务
```bash
# 进入代码沙箱项目目录
cd micode-code-sandbox

# 确保 Docker 服务已启动
docker --version

# 安装依赖并编译
mvn clean install

# 启动服务
mvn spring-boot:run
# 或者
java -jar target/micode-code-sandbox-0.0.1-SNAPSHOT.jar
```

**验证**: 服务启动后会自动拉取 `openjdk:8-alpine` Docker 镜像

#### 4.3 启动前端服务
```bash
# 进入前端项目目录
cd micode-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run serve
```

**验证**: 访问 http://localhost:8080 查看前端界面

### 5. 服务端口说明

| 服务 | 端口 | 访问地址 | 说明 |
|------|------|----------|------|
| 前端服务 | 8080 | http://localhost:8080 | Vue.js 开发服务器 |
| 后端服务 | 11091 | http://localhost:11091/api | Spring Boot 后端 API |
| API 文档 | 11091 | http://localhost:11091/api/doc.html | Knife4j API 文档 |
| 代码沙箱 | 11090 | http://localhost:11090 | 代码执行服务 |

### 6. 默认账号密码

**管理员账号**:
- 用户名: `admin`
- 密码: `admin123`

**测试用户**:
- 用户名: `test`
- 密码: `test123`

*注意: 首次启动需要通过注册功能创建账号*

### 7. 常见问题解决

#### 7.1 数据库连接失败
- 检查 MySQL 服务是否启动
- 验证数据库用户名密码是否正确
- 确认数据库 `codedb` 是否已创建

#### 7.2 Redis 连接失败
- 检查 Redis 服务是否启动
- 验证 Redis 连接配置是否正确
- 检查网络连接是否正常

#### 7.3 RabbitMQ 连接失败
- 检查 RabbitMQ 服务是否启动: `sudo systemctl status rabbitmq-server`
- 验证用户名密码是否正确（默认 guest/guest）
- 确认端口 5672 是否开放
- 检查虚拟主机配置是否正确

#### 7.4 Docker 相关问题
- 确保 Docker 服务已启动
- 检查 Docker 镜像拉取权限
- 验证 Docker 网络配置

#### 7.5 前端编译错误
- 清除 node_modules: `rm -rf node_modules && npm install`
- 检查 Node.js 版本是否符合要求
- 验证网络连接，确保能正常下载依赖

### 8. 生产环境部署

#### 8.1 前端部署
```bash
# 构建生产版本
npm run build

# 将 dist 目录部署到 Web 服务器（如 Nginx）
```

#### 8.2 后端部署
```bash
# 打包应用
mvn clean package

# 部署 JAR 文件到服务器
java -jar micode-backend-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

#### 8.3 数据库配置
- 生产环境建议使用独立的 MySQL 实例
- 配置数据库连接池参数
- 设置合适的数据库用户权限

### 9. 技术栈详情

#### 9.1 后端技术栈
- **框架**: Spring Boot 2.7.6
- **ORM**: MyBatis Plus 3.5.2
- **数据库**: MySQL 5.7+
- **缓存**: Redis
- **消息队列**: RabbitMQ
- **API 文档**: Knife4j 4.4.0
- **工具库**: Hutool 5.8.8

#### 9.2 前端技术栈
- **框架**: Vue 3.2.13
- **语言**: TypeScript 4.5.5
- **UI 库**: Element Plus 2.7.3
- **状态管理**: Pinia 2.1.7
- **路由**: Vue Router 4.0.3
- **代码编辑器**: Monaco Editor 0.48.0
- **Markdown**: ByteMD 1.21.0

#### 9.3 代码沙箱技术栈
- **容器化**: Docker
- **镜像**: openjdk:8-alpine
- **Java 客户端**: docker-java 3.2.13

### 10. 开发建议

1. **代码规范**: 项目已配置 ESLint 和 Prettier，请遵循代码规范
2. **Git 提交**: 使用规范的提交信息格式
3. **测试**: 编写单元测试和集成测试
4. **文档**: 及时更新 API 文档和代码注释
5. **安全**: 注意数据验证和权限控制

---

**联系方式**: 如遇到问题，请联系项目维护者
**项目地址**: [GitHub 仓库地址]
**更新时间**: 2024年12月
